<?php

namespace App\Http\Controllers;

use App\Models\Consulta;
use App\Models\Paciente;
use App\Models\Dentista;
use App\Models\HistoricoPaciente;
use App\Models\FichaCounter;
use App\Traits\LogsActionHistory;
use Illuminate\Http\Request;

class ConsultaController extends Controller
{
    use LogsActionHistory;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->payload();

        $consultas = Consulta::with(['paciente', 'dentista'])
        ->whereHas('paciente', function ($q) use ($user) {
            $q->where('clinica_id', $user['clinica']['id']);
        })
        ->get();

        return responseSuccess(['data' => $consultas]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $user = auth()->user();
        $userPayload = auth()->payload();

        // Helper para verificar se deve criar paciente
        $criarPaciente = isset($data['criar_paciente']) && ($data['criar_paciente'] === true || $data['criar_paciente'] === 'true' || $data['criar_paciente'] === 1 || $data['criar_paciente'] === '1');

        // Verificar se deve criar um paciente automaticamente
        if ($criarPaciente) {
            // Validar campos obrigatórios para criação de paciente
            if (!isset($data['paciente_nome']) || empty($data['paciente_nome'])) {
                return responseError(['message' => 'O campo paciente_nome é obrigatório quando criar_paciente for true']);
            }

            if (!isset($data['dentista_id']) || empty($data['dentista_id'])) {
                return responseError(['message' => 'O campo dentista_id é obrigatório quando criar_paciente for true']);
            }

            // Buscar o dentista para obter a clinica_id
            $dentista = Dentista::find($data['dentista_id']);
            if (!$dentista) {
                return responseError(['message' => 'Dentista não encontrado']);
            }

            // Verificar se o dentista pertence à clínica do usuário (se não for admin do sistema)
            if (!$userPayload['system_admin'] && $dentista->clinica_id !== $userPayload['clinica']['id']) {
                return responseError(['message' => 'Dentista não pertence à clínica atual']);
            }

            // Criar o novo paciente
            $paciente = new Paciente();
            $paciente->nome = $data['paciente_nome'];
            $paciente->dentista_id = $data['dentista_id'];
            $paciente->clinica_id = $dentista->clinica_id;
            $paciente->observacoes = isset($data['observacoes_paciente']) ? $data['observacoes_paciente'] : '';
            $paciente->public_token = createToken(50);
            $paciente->id_ficha = FichaCounter::getNextIdFicha($dentista->clinica_id);

            $paciente->save();

            // Log the patient creation action
            $this->logCreateAction($paciente, null, $request, "Created patient during consultation booking: {$paciente->nome}");

            // Usar o ID do paciente recém-criado na consulta
            $data['paciente_id'] = $paciente->id;
        }

        // Definir valores padrão se não forem fornecidos
        $data['status'] = $data['status'] ?? 'agendada';
        $data['reagendamentos'] = $data['reagendamentos'] ?? 0;
        $data['user_id'] = $user->id;

        // Verificar se paciente_id está definido (apenas se não estamos criando um paciente)
        if (!$criarPaciente) {
            if (!isset($data['paciente_id']) || empty($data['paciente_id'])) {
                return responseError(['message' => 'O campo paciente_id é obrigatório para criar uma consulta']);
            }
        }

        // Verificação final: garantir que paciente_id está definido após todo o processamento
        if (!isset($data['paciente_id']) || empty($data['paciente_id'])) {
            return responseError(['message' => 'Erro interno: paciente_id não foi definido corretamente']);
        }

        // Se criou um paciente, adicionar o nome do paciente na consulta
        if ($criarPaciente) {
            $data['paciente_nome'] = $data['paciente_nome'];
        } else if (isset($data['paciente_id']) && (!isset($data['paciente_nome']) || empty($data['paciente_nome']))) {
            // Se não criou paciente mas tem paciente_id e não tem paciente_nome, buscar o nome do paciente
            $pacienteExistente = Paciente::find($data['paciente_id']);
            if ($pacienteExistente) {
                $data['paciente_nome'] = $pacienteExistente->nome;
            }
        }

        // Filtrar apenas os campos que pertencem à tabela consultas
        $consultaData = array_intersect_key($data, array_flip([
            'paciente_id', 'paciente_nome', 'dentista_id', 'data', 'horario', 'observacoes',
            'status', 'confirmada', 'categoria', 'reagendamentos', 'user_id', 'valor', 'notas', 'convenio_id'
        ]));

        $consulta = Consulta::create($consultaData);

        // Log the consultation creation action
        $this->logCreateAction($consulta, null, $request, "Created consultation for patient: {$consulta->paciente_nome}");

        // Buscar o paciente para obter o nome
        $paciente = Paciente::find($consulta->paciente_id);

        return responseSuccess(['data' => $consulta]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Consulta $consulta)
    {
        $consulta->load(['paciente', 'dentista']);

        return responseSuccess(['data' => $consulta]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Consulta $consulta)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Consulta $consulta)
    {
        $data = $request->all();
        $user = auth()->user();
        $userPayload = auth()->payload();

        // Helper para verificar se deve criar paciente
        $criarPaciente = isset($data['criar_paciente']) && ($data['criar_paciente'] === true || $data['criar_paciente'] === 'true' || $data['criar_paciente'] === 1 || $data['criar_paciente'] === '1');

        // Verificar se deve criar um paciente automaticamente
        if ($criarPaciente) {
            // Validar campos obrigatórios para criação de paciente
            if (!isset($data['paciente_nome']) || empty($data['paciente_nome'])) {
                return responseError(['message' => 'O campo paciente_nome é obrigatório quando criar_paciente for true']);
            }

            if (!isset($data['dentista_id']) || empty($data['dentista_id'])) {
                return responseError(['message' => 'O campo dentista_id é obrigatório quando criar_paciente for true']);
            }

            // Buscar o dentista para obter a clinica_id
            $dentista = Dentista::find($data['dentista_id']);
            if (!$dentista) {
                return responseError(['message' => 'Dentista não encontrado']);
            }

            // Verificar se o dentista pertence à mesma clínica do usuário (se não for system_admin)
            if (!$userPayload['system_admin'] && $dentista->clinica_id !== $userPayload['clinica']['id']) {
                return responseError(['message' => 'Dentista não pertence à clínica atual']);
            }

            // Criar o novo paciente
            $paciente = new Paciente();
            $paciente->nome = $data['paciente_nome'];
            $paciente->dentista_id = $data['dentista_id'];
            $paciente->clinica_id = $dentista->clinica_id;
            $paciente->observacoes = isset($data['observacoes_paciente']) ? $data['observacoes_paciente'] : '';
            $paciente->public_token = createToken(50);
            $paciente->id_ficha = FichaCounter::getNextIdFicha($dentista->clinica_id);

            $paciente->save();

            // Usar o ID do paciente recém-criado na consulta
            $data['paciente_id'] = $paciente->id;
        }

        // Definir user_id se não estiver presente
        if (!isset($data['user_id'])) {
            $data['user_id'] = $user->id;
        }

        // Se criou um paciente, adicionar o nome do paciente na consulta
        if ($criarPaciente) {
            $data['paciente_nome'] = $data['paciente_nome'];
        } else if (isset($data['paciente_id']) && (!isset($data['paciente_nome']) || empty($data['paciente_nome']))) {
            // Se não criou paciente mas tem paciente_id e não tem paciente_nome, buscar o nome do paciente
            $pacienteExistente = Paciente::find($data['paciente_id']);
            if ($pacienteExistente) {
                $data['paciente_nome'] = $pacienteExistente->nome;
            }
        }

        // Filtrar apenas os campos que pertencem à tabela consultas
        $consultaData = array_intersect_key($data, array_flip([
            'paciente_id', 'paciente_nome', 'dentista_id', 'data', 'horario', 'observacoes',
            'status', 'categoria', 'reagendamentos', 'user_id', 'valor', 'notas', 'convenio_id'
        ]));

        $dadosAntigos = $consulta->toArray();
        $consulta->update($consultaData);
        $dadosNovos = $consulta->toArray();

        // Log the consultation update action
        $this->logUpdateAction($consulta, $dadosAntigos, $dadosNovos, $request, "Updated consultation for patient: {$consulta->paciente_nome}");

        // Recarregar a consulta com os relacionamentos
        $consulta->load(['paciente', 'dentista']);

        return responseSuccess(['data' => $consulta]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Consulta $consulta)
    {
        // Capture consultation data before deletion
        $consultaData = $consulta->toArray();

        $consulta->delete();

        // Log the consultation deletion action
        $this->logDeleteAction($consulta, $consultaData, request(), "Deleted consultation for patient: {$consultaData['paciente_nome']}");

        return responseSuccess();
    }

    /**
     * Get consultations for a specific patient within a time range.
     *
     * @param int $paciente_id
     * @param string $timestamp_inicio
     * @param string $timestamp_fim
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConsultasByPacienteAndTimeRange($paciente_id, $timestamp_inicio, $timestamp_fim)
    {
        $user = auth()->payload();

        $query = Consulta::with(['paciente', 'dentista'])
            ->where('paciente_id', $paciente_id)
            ->where('horario', '>=', date('Y-m-d H:i:s', $timestamp_inicio))
            ->where('horario', '<=', date('Y-m-d H:i:s', $timestamp_fim));

        // Se não for admin do sistema, filtra apenas as consultas dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $consultas = $query->get();

        return responseSuccess(['data' => $consultas]);
    }

    /**
     * Get all consultations within a time range.
     *
     * @param string $timestamp_inicio
     * @param string $timestamp_fim
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConsultasByTimeRange($timestamp_inicio, $timestamp_fim)
    {
        $user = auth()->payload();

        $query = Consulta::with(['paciente', 'dentista'])
            ->where('horario', '>=', date('Y-m-d H:i:s', $timestamp_inicio))
            ->where('horario', '<=', date('Y-m-d H:i:s', $timestamp_fim));

        // Se não for admin do sistema, filtra apenas as consultas dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $consultas = $query->get();

        return responseSuccess(['data' => $consultas]);
    }

    /**
     * Get all consultations for a specific patient.
     *
     * @param int $paciente_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConsultasByPaciente($paciente_id)
    {
        $user = auth()->payload();

        $query = Consulta::with(['paciente', 'dentista'])
            ->where('paciente_id', $paciente_id);

        // Se não for admin do sistema, filtra apenas as consultas dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $consultas = $query->get();

        return responseSuccess(['data' => $consultas]);
    }
}
